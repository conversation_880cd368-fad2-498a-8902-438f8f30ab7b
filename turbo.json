{"$schema": "https://turborepo.com/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "//#format": {}, "//#lint": {"cache": false}, "check-types": {"dependsOn": ["^check-types"]}, "dev": {"cache": false, "persistent": true}, "db:generate": {"cache": false}, "db:migrate": {"cache": false}, "db:push": {"cache": false}, "db:studio": {"cache": false, "persistent": true}}}