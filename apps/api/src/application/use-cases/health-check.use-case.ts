import { Injectable } from '@nestjs/common';
import { UseCase, Result } from '../../shared/types';
import { HealthCheckRequest, HealthCheckResponse } from '../dtos';
import { IHealthService } from '../interfaces';

@Injectable()
export class HealthCheckUseCase implements UseCase<HealthCheckRequest, HealthCheckResponse> {
  constructor(private readonly healthService: IHealthService) {}

  async execute(request: HealthCheckRequest): Promise<Result<HealthCheckResponse>> {
    try {
      const healthData = await this.healthService.checkHealth();
      
      const response: HealthCheckResponse = {
        status: healthData.status,
        timestamp: healthData.timestamp,
        uptime: healthData.uptime,
        ...(request.includeDetails && { details: healthData.details }),
      };

      return {
        success: true,
        data: response,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error : new Error('Unknown error occurred'),
      };
    }
  }
}
