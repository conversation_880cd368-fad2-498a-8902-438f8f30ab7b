import { z } from 'zod';

// Request DTOs
export const HealthCheckRequestSchema = z.object({
  includeDetails: z.boolean().optional().default(false),
});

export type HealthCheckRequest = z.infer<typeof HealthCheckRequestSchema>;

// Response DTOs
export const HealthCheckResponseSchema = z.object({
  status: z.enum(['healthy', 'unhealthy']),
  timestamp: z.date(),
  uptime: z.number(),
  details: z.record(z.any()).optional(),
});

export type HealthCheckResponse = z.infer<typeof HealthCheckResponseSchema>;
