import { BaseEntity } from './common.types';

export interface Listing extends BaseEntity {
  title: string;
  description: string;
  price: number;
  currency: string;
  location: {
    address: string;
    city: string;
    state: string;
    zipCode: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  propertyType: 'apartment' | 'house' | 'condo' | 'townhouse' | 'other';
  bedrooms: number;
  bathrooms: number;
  squareFootage?: number;
  amenities: string[];
  images: string[];
  status: 'active' | 'pending' | 'sold' | 'inactive';
  ownerId: string;
  contactInfo: {
    email: string;
    phone?: string;
  };
}

export interface ListingFilters {
  minPrice?: number;
  maxPrice?: number;
  propertyType?: Listing['propertyType'];
  minBedrooms?: number;
  maxBedrooms?: number;
  minBathrooms?: number;
  maxBathrooms?: number;
  city?: string;
  state?: string;
  status?: Listing['status'];
  amenities?: string[];
}
