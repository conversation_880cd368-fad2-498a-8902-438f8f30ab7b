{"permissions": {"allow": ["WebFetch(domain:docs.anthropic.com)", "mcp__serena__list_dir", "WebFetch(domain:ui.shadcn.com)", "mcp__serena__get_symbols_overview", "Bash(bun dlx:*)", "Bash(bunx:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(bun install:*)", "mcp__serena__find_file", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebSearch", "mcp__serena__search_for_pattern", "Bash(bun x:*)", "mcp__serena__check_onboarding_performed", "Bash(bun add:*)", "mcp__serena__find_symbol", "WebFetch(domain:github.com)", "WebFetch(domain:hono.dev)", "mcp__serena__write_memory", "WebFetch(domain:stackoverflow.com)", "WebFetch(domain:www.prisma.io)", "WebFetch(domain:orm.drizzle.team)", "Bash(bun remove:*)", "Bash(bun run:*)", "mcp__serena__replace_symbol_body", "Bash(npx tsc:*)", "Bash(bun list:*)", "Bash(bun show:*)", "<PERSON><PERSON>(cat:*)", "Bash(node:*)"], "deny": [], "ask": []}}