{"$schema": "https://json.schemastore.org/tsconfig.json", "display": "Base", "compilerOptions": {"esModuleInterop": true, "skipLibCheck": true, "target": "esnext", "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "useDefineForClassFields": true, "strict": true, "noUncheckedIndexedAccess": true, "noImplicitOverride": true, "noUncheckedSideEffectImports": true, "incremental": true, "module": "preserve", "noEmit": true}}