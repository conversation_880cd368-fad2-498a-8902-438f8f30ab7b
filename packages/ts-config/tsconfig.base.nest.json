{"$schema": "https://json.schemastore.org/tsconfig.json", "display": "Base NestJS", "compilerOptions": {"target": "es2020", "module": "commonjs", "emitDecoratorMetadata": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "allowSyntheticDefaultImports": true, "removeComments": true, "incremental": true, "skipLibCheck": true, "noEmit": false, "outDir": "./dist", "declaration": true, "sourceMap": true, "esModuleInterop": true, "resolveJsonModule": true, "strict": true}}