/**
 * Email Service Usage Examples for Homeo Real Estate Platform
 *
 * This file demonstrates how to use the @repo/mail package with React Email and <PERSON>send
 * for various real estate scenarios.
 */

import {
  AppointmentConfirmationEmail,
  MailService,
  MailServiceError,
  PropertyAlertEmail,
  PropertyInquiryEmail,
  type SendEmailOptions,
  WelcomeEmail,
} from '../src';

// Initialize the mail service
const mailService = new MailService({
  apiKey: process.env.RESEND_API_KEY,
  defaultFromEmail: '<EMAIL>',
});

// Example 1: Send welcome email to new user
export async function sendWelcomeEmail(userEmail: string, userName: string) {
  try {
    const result = await mailService.send({
      to: userEmail,
      subject: 'Welcome to Homeo!',
      template: WelcomeEmail({ name: userName }),
      tags: [
        { name: 'category', value: 'welcome' },
        { name: 'user_type', value: 'new' },
      ],
    });

    console.log('Welcome email sent:', result.data?.id);
    return result;
  } catch (error) {
    if (error instanceof MailServiceError) {
      console.error('Mail service error:', error.code, error.message);
    } else {
      console.error('Unexpected error:', error);
    }
    throw error;
  }
}

// Example 2: Send property inquiry notification to agent
export async function sendPropertyInquiry({
  agentEmail,
  agentName,
  customerName,
  customerEmail,
  customerPhone,
  propertyTitle,
  propertyAddress,
  propertyPrice,
  propertyUrl,
  message,
}: {
  agentEmail: string;
  agentName: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  propertyTitle: string;
  propertyAddress: string;
  propertyPrice: string;
  propertyUrl: string;
  message?: string;
}) {
  try {
    const result = await mailService.send({
      to: agentEmail,
      subject: `New inquiry for ${propertyTitle}`,
      template: PropertyInquiryEmail({
        agentName,
        customerName,
        customerEmail,
        customerPhone,
        propertyTitle,
        propertyAddress,
        propertyPrice,
        propertyUrl,
        message,
      }),
      replyTo: customerEmail,
      tags: [
        { name: 'category', value: 'inquiry' },
        { name: 'property_type', value: 'residential' },
      ],
    });

    console.log('Property inquiry sent:', result.data?.id);
    return result;
  } catch (error) {
    if (error instanceof MailServiceError) {
      console.error('Failed to send inquiry:', error.code, error.message);
    }
    throw error;
  }
}

// Example 3: Send property alerts to users
export async function sendPropertyAlerts({
  userEmail,
  userName,
  searchCriteria,
  properties,
  unsubscribeToken,
}: {
  userEmail: string;
  userName: string;
  searchCriteria: string;
  properties: Array<{
    title: string;
    address: string;
    price: string;
    bedrooms: number;
    bathrooms: number;
    squareMeters: number;
    url: string;
  }>;
  unsubscribeToken: string;
}) {
  if (properties.length === 0) {
    console.log('No properties to send in alert');
    return;
  }

  try {
    const result = await mailService.send({
      to: userEmail,
      subject: `${properties.length} New Properties Match Your Search`,
      template: PropertyAlertEmail({
        userName,
        searchCriteria,
        properties,
        unsubscribeUrl: `https://homeo.com/unsubscribe/alerts/${unsubscribeToken}`,
      }),
      tags: [
        { name: 'category', value: 'alert' },
        { name: 'property_count', value: properties.length.toString() },
      ],
    });

    console.log('Property alert sent:', result.data?.id);
    return result;
  } catch (error) {
    if (error instanceof MailServiceError) {
      console.error(
        'Failed to send property alert:',
        error.code,
        error.message
      );
    }
    throw error;
  }
}

// Example 4: Send appointment confirmation
export async function sendAppointmentConfirmation({
  customerEmail,
  customerName,
  agentName,
  agentPhone,
  agentEmail,
  propertyTitle,
  propertyAddress,
  appointmentDate,
  appointmentTime,
  notes,
  propertyUrl,
  appointmentId,
}: {
  customerEmail: string;
  customerName: string;
  agentName: string;
  agentPhone: string;
  agentEmail: string;
  propertyTitle: string;
  propertyAddress: string;
  appointmentDate: string;
  appointmentTime: string;
  notes?: string;
  propertyUrl: string;
  appointmentId: string;
}) {
  try {
    const result = await mailService.send({
      to: customerEmail,
      subject: `Appointment Confirmed: ${propertyTitle}`,
      template: AppointmentConfirmationEmail({
        customerName,
        agentName,
        agentPhone,
        agentEmail,
        propertyTitle,
        propertyAddress,
        appointmentDate,
        appointmentTime,
        notes,
        propertyUrl,
        rescheduleUrl: `https://homeo.com/appointment/reschedule/${appointmentId}`,
        cancelUrl: `https://homeo.com/appointment/cancel/${appointmentId}`,
      }),
      replyTo: agentEmail,
      tags: [
        { name: 'category', value: 'appointment' },
        { name: 'status', value: 'confirmed' },
      ],
    });

    console.log('Appointment confirmation sent:', result.data?.id);
    return result;
  } catch (error) {
    if (error instanceof MailServiceError) {
      console.error(
        'Failed to send appointment confirmation:',
        error.code,
        error.message
      );
    }
    throw error;
  }
}

// Example 5: Send batch emails (e.g., newsletter to multiple users)
export async function sendNewsletterBatch(
  subscribers: Array<{ email: string; name: string }>,
  newsletterContent: {
    subject: string;
    template: React.ReactElement;
  }
) {
  const emails: SendEmailOptions[] = subscribers.map((subscriber) => ({
    to: subscriber.email,
    subject: newsletterContent.subject,
    template: newsletterContent.template,
    tags: [
      { name: 'category', value: 'newsletter' },
      { name: 'batch', value: 'monthly' },
    ],
  }));

  try {
    const results = await mailService.sendBatch(emails);
    console.log(`Newsletter sent to ${results.length} subscribers`);
    return results;
  } catch (error) {
    if (error instanceof MailServiceError) {
      console.error('Batch send failed:', error.code, error.message);
    }
    throw error;
  }
}

// Example 6: Error handling patterns
export async function handleEmailErrors() {
  try {
    await mailService.send({
      to: 'invalid-email',
      subject: 'Test',
      template: WelcomeEmail({ name: 'Test User' }),
    });
  } catch (error) {
    if (error instanceof MailServiceError) {
      // Handle specific mail service errors
      switch (error.code) {
        case 'MISSING_API_KEY':
          console.error('API key not configured');
          break;
        case 'SEND_FAILED':
          console.error('Email delivery failed:', error.message);
          break;
        case 'UNKNOWN_ERROR':
          console.error('Unexpected error occurred:', error.cause);
          break;
        default:
          console.error('Mail service error:', error.message);
      }
    } else {
      // Handle other types of errors
      console.error('Unexpected error:', error);
    }
  }
}

// Example 7: Using custom configuration
export function createCustomMailService() {
  return new MailService({
    apiKey: 'your-custom-api-key',
    defaultFromEmail: '<EMAIL>',
  });
}
