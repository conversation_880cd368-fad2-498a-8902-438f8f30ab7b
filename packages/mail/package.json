{"name": "@repo/mail", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./templates": "./src/templates/index.ts", "./service": "./src/service.ts"}, "scripts": {"dev": "email dev", "build": "email build", "preview": "email preview", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "react-email": "^3.0.1", "typescript": "5.9.2"}, "dependencies": {"@react-email/components": "^0.0.25", "@react-email/render": "^1.0.1", "react": "^19.1.0", "react-dom": "^19.1.0", "resend": "^4.0.1"}}