import { render } from '@react-email/render';
import type { ReactElement } from 'react';
import {
  type CreateEmailOptions,
  type CreateEmailResponse,
  Resend,
} from 'resend';

export interface SendEmailOptions {
  to: string | string[];
  from?: string;
  subject: string;
  template: ReactElement;
  text?: string;
  attachments?: CreateEmailOptions['attachments'];
  tags?: CreateEmailOptions['tags'];
  replyTo?: string | string[];
}

export interface SendTextEmailOptions {
  to: string | string[];
  from?: string;
  subject: string;
  text: string;
  attachments?: CreateEmailOptions['attachments'];
  tags?: CreateEmailOptions['tags'];
  replyTo?: string | string[];
}

export interface MailServiceConfig {
  apiKey?: string;
  defaultFromEmail?: string;
}

export class MailServiceError extends Error {
  constructor(
    message: string,
    public readonly cause?: unknown,
    public readonly code?: string
  ) {
    super(message);
    this.name = 'MailServiceError';
  }
}

export class MailService {
  private readonly resend: Resend;
  private readonly defaultFromEmail: string;

  constructor(config: MailServiceConfig = {}) {
    const apiKey = config.apiKey || process.env.RESEND_API_KEY;
    if (!apiKey) {
      throw new MailServiceError(
        'Resend API key is required. Provide it via constructor options or RESEND_API_KEY environment variable.',
        undefined,
        'MISSING_API_KEY'
      );
    }

    this.resend = new Resend(apiKey);
    this.defaultFromEmail =
      config.defaultFromEmail ||
      process.env.RESEND_FROM_EMAIL ||
      '<EMAIL>';
  }

  async send(options: SendEmailOptions): Promise<CreateEmailResponse> {
    try {
      const html = await render(options.template, {
        pretty: false,
      });

      const result = await this.resend.emails.send({
        to: options.to,
        from: options.from || this.defaultFromEmail,
        subject: options.subject,
        html,
        text: options.text,
        attachments: options.attachments,
        tags: options.tags,
        replyTo: options.replyTo,
      });

      if (result.error) {
        throw new MailServiceError(
          `Failed to send email: ${result.error.message}`,
          result.error,
          'SEND_FAILED'
        );
      }

      return result;
    } catch (error) {
      if (error instanceof MailServiceError) {
        throw error;
      }

      throw new MailServiceError(
        'An unexpected error occurred while sending email',
        error,
        'UNKNOWN_ERROR'
      );
    }
  }

  async sendText(options: SendTextEmailOptions): Promise<CreateEmailResponse> {
    try {
      const result = await this.resend.emails.send({
        to: options.to,
        from: options.from || this.defaultFromEmail,
        subject: options.subject,
        text: options.text,
        attachments: options.attachments,
        tags: options.tags,
        replyTo: options.replyTo,
      });

      if (result.error) {
        throw new MailServiceError(
          `Failed to send text email: ${result.error.message}`,
          result.error,
          'SEND_FAILED'
        );
      }

      return result;
    } catch (error) {
      if (error instanceof MailServiceError) {
        throw error;
      }

      throw new MailServiceError(
        'An unexpected error occurred while sending text email',
        error,
        'UNKNOWN_ERROR'
      );
    }
  }

  async sendBatch(emails: SendEmailOptions[]): Promise<CreateEmailResponse[]> {
    try {
      const results = await Promise.allSettled(
        emails.map((email) => this.send(email))
      );

      return results.map((result, index) => {
        if (result.status === 'rejected') {
          throw new MailServiceError(
            `Failed to send email ${index + 1} in batch: ${result.reason}`,
            result.reason,
            'BATCH_SEND_FAILED'
          );
        }
        return result.value;
      });
    } catch (error) {
      if (error instanceof MailServiceError) {
        throw error;
      }

      throw new MailServiceError(
        'An unexpected error occurred while sending batch emails',
        error,
        'BATCH_UNKNOWN_ERROR'
      );
    }
  }
}

// Default instance
export const mailService = new MailService();
