import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Hr,
  Html,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface PropertyProps {
  title: string;
  address: string;
  price: string;
  bedrooms: number;
  bathrooms: number;
  squareMeters: number;
  url: string;
}

interface PropertyAlertEmailProps {
  userName: string;
  searchCriteria: string;
  properties: PropertyProps[];
  unsubscribeUrl: string;
}

export const PropertyAlertEmail = ({
  userName,
  searchCriteria,
  properties,
  unsubscribeUrl,
}: PropertyAlertEmailProps) => (
  <Html>
    <Head />
    <Preview>
      {properties.length} new{' '}
      {properties.length === 1 ? 'property matches' : 'properties match'} your
      search criteria
    </Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>New Property Alert</Heading>

        <Text style={text}>Hi {userName},</Text>

        <Text style={text}>
          Great news! We found {properties.length} new{' '}
          {properties.length === 1 ? 'property' : 'properties'} that{' '}
          {properties.length === 1 ? 'matches' : 'match'} your search criteria:
        </Text>

        <Section style={searchSection}>
          <Text style={searchCriteria}>"{searchCriteria}"</Text>
        </Section>

        {properties.map((property, index) => (
          <Section key={index} style={propertyCard}>
            <Heading style={h2}>{property.title}</Heading>
            <Text style={propertyDetails}>
              📍 {property.address}
              <br />💰 {property.price}
              <br />
              🛏️ {property.bedrooms} bed • 🛁 {property.bathrooms} bath • 📐{' '}
              {property.squareMeters} m²
            </Text>
            <Button href={property.url} style={button}>
              View Property
            </Button>
            {index < properties.length - 1 && <Hr style={propertyHr} />}
          </Section>
        ))}

        <Hr style={hr} />

        <Section style={footerSection}>
          <Text style={footerText}>
            Don't miss out! Properties matching your criteria tend to go fast.
          </Text>
          <Button href="https://homeo.com/search" style={primaryButton}>
            View All Matching Properties
          </Button>

          <Text style={unsubscribeText}>
            <a href={unsubscribeUrl} style={unsubscribeLink}>
              Unsubscribe from property alerts
            </a>
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

PropertyAlertEmail.PreviewProps = {
  userName: 'Sarah',
  searchCriteria: '2-3 bedrooms • Downtown Toronto • $600k-$800k',
  properties: [
    {
      title: 'Modern 2BR Condo with City Views',
      address: '456 Bay Street, Toronto, ON',
      price: '$725,000',
      bedrooms: 2,
      bathrooms: 2,
      squareMeters: 85,
      url: 'https://homeo.com/property/456',
    },
    {
      title: 'Renovated 3BR Townhouse',
      address: '789 Queen Street West, Toronto, ON',
      price: '$780,000',
      bedrooms: 3,
      bathrooms: 2,
      squareMeters: 120,
      url: 'https://homeo.com/property/789',
    },
  ],
  unsubscribeUrl: 'https://homeo.com/unsubscribe/alerts/abc123',
} as PropertyAlertEmailProps;

export default PropertyAlertEmail;

const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '40px',
  margin: '0 0 20px',
};

const h2 = {
  color: '#333',
  fontSize: '18px',
  fontWeight: '600',
  lineHeight: '24px',
  margin: '0 0 12px',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '0 0 20px',
};

const searchSection = {
  backgroundColor: '#e3f2fd',
  borderRadius: '8px',
  padding: '16px',
  margin: '20px 0',
  textAlign: 'center' as const,
};

const searchCriteria = {
  color: '#1976d2',
  fontSize: '16px',
  fontWeight: '600',
  margin: '0',
};

const propertyCard = {
  backgroundColor: '#f8f9fa',
  borderRadius: '8px',
  padding: '20px',
  margin: '16px 0',
};

const propertyDetails = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0 0 16px',
};

const propertyHr = {
  borderColor: '#ddd',
  margin: '16px 0 0',
};

const button = {
  backgroundColor: '#007bff',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '14px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 16px',
  fontWeight: '600',
};

const primaryButton = {
  backgroundColor: '#28a745',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
  fontWeight: '600',
  margin: '16px 0',
};

const footerSection = {
  textAlign: 'center' as const,
  margin: '20px 0',
};

const footerText = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 20px',
};

const unsubscribeText = {
  color: '#666',
  fontSize: '12px',
  lineHeight: '16px',
  margin: '20px 0 0',
};

const unsubscribeLink = {
  color: '#666',
  textDecoration: 'underline',
};

const hr = {
  borderColor: '#eee',
  margin: '20px 0',
};
