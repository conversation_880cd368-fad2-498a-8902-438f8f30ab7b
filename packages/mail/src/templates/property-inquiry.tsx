import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Img,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface PropertyInquiryEmailProps {
  agentName: string;
  customerName: string;
  customerEmail: string;
  customerPhone?: string;
  propertyTitle: string;
  propertyAddress: string;
  propertyPrice: string;
  propertyUrl: string;
  message?: string;
}

export const PropertyInquiryEmail = ({
  agentName,
  customerName,
  customerEmail,
  customerPhone,
  propertyTitle,
  propertyAddress,
  propertyPrice,
  propertyUrl,
  message,
}: PropertyInquiryEmailProps) => (
  <Html>
    <Head />
    <Preview>New inquiry for {propertyTitle}</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>New Property Inquiry</Heading>

        <Text style={text}>Hi {agentName},</Text>

        <Text style={text}>
          You have received a new inquiry for one of your listings:
        </Text>

        <Section style={propertySection}>
          <Heading style={h2}>{propertyTitle}</Heading>
          <Text style={propertyDetails}>
            📍 {propertyAddress}
            <br />💰 {propertyPrice}
          </Text>
          <Button href={propertyUrl} style={button}>
            View Property
          </Button>
        </Section>

        <Hr style={hr} />

        <Section style={customerSection}>
          <Heading style={h3}>Customer Details</Heading>
          <Text style={customerDetails}>
            <strong>Name:</strong> {customerName}
            <br />
            <strong>Email:</strong> {customerEmail}
            {customerPhone && (
              <>
                <br />
                <strong>Phone:</strong> {customerPhone}
              </>
            )}
          </Text>

          {message && (
            <>
              <Text style={text}>
                <strong>Message:</strong>
              </Text>
              <Text style={messageText}>{message}</Text>
            </>
          )}
        </Section>

        <Hr style={hr} />

        <Text style={footerText}>
          Please respond to this inquiry promptly to provide the best customer
          service.
        </Text>
      </Container>
    </Body>
  </Html>
);

PropertyInquiryEmail.PreviewProps = {
  agentName: 'Sarah Johnson',
  customerName: 'John Smith',
  customerEmail: '<EMAIL>',
  customerPhone: '+****************',
  propertyTitle: 'Beautiful 3BR Family Home in Downtown',
  propertyAddress: '123 Main Street, Toronto, ON',
  propertyPrice: '$750,000',
  propertyUrl: 'https://homeo.com/property/123',
  message:
    "Hi, I'm interested in viewing this property this weekend. When would be a good time?",
} as PropertyInquiryEmailProps;

export default PropertyInquiryEmail;

const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '40px',
  margin: '0 0 20px',
};

const h2 = {
  color: '#333',
  fontSize: '20px',
  fontWeight: '600',
  lineHeight: '28px',
  margin: '0 0 12px',
};

const h3 = {
  color: '#333',
  fontSize: '18px',
  fontWeight: '600',
  lineHeight: '24px',
  margin: '0 0 12px',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '0 0 20px',
};

const propertySection = {
  backgroundColor: '#f8f9fa',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
};

const propertyDetails = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0 0 16px',
};

const customerSection = {
  margin: '20px 0',
};

const customerDetails = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0 0 16px',
};

const messageText = {
  backgroundColor: '#f8f9fa',
  borderLeft: '4px solid #007bff',
  padding: '12px 16px',
  margin: '8px 0 20px',
  fontStyle: 'italic',
  color: '#555',
  fontSize: '14px',
  lineHeight: '20px',
};

const button = {
  backgroundColor: '#007bff',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  padding: '12px 20px',
  fontWeight: '600',
};

const hr = {
  borderColor: '#eee',
  margin: '20px 0',
};

const footerText = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '20px 0 0',
  fontStyle: 'italic',
};
