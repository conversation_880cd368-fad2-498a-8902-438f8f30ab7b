import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Text,
} from '@react-email/components';

interface WelcomeEmailProps {
  name: string;
}

export const WelcomeEmail = ({ name }: WelcomeEmailProps) => (
  <Html>
    <Head />
    <Preview>Welcome to Homeo - Your real estate journey starts here</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>Welcome to Homeo!</Heading>
        <Text style={text}>Hi {name},</Text>
        <Text style={text}>
          Welcome to Homeo! We're excited to help you find your perfect home.
          Our platform makes it easy to search, filter, and connect with the
          best real estate listings.
        </Text>
        <Text style={text}>
          Get started by exploring our latest listings or setting up your search
          preferences.
        </Text>
        <Text style={text}>
          Happy house hunting!
          <br />
          The Homeo Team
        </Text>
      </Container>
    </Body>
  </Html>
);

WelcomeEmail.PreviewProps = {
  name: '<PERSON>',
} as WelcomeEmailProps;

export default WelcomeEmail;

const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '40px',
  margin: '0 0 20px',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '0 0 20px',
};
