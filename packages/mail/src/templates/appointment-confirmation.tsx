import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Hr,
  Html,
  Preview,
  Section,
  Text,
} from '@react-email/components';

interface AppointmentConfirmationEmailProps {
  customerName: string;
  agentName: string;
  agentPhone: string;
  agentEmail: string;
  propertyTitle: string;
  propertyAddress: string;
  appointmentDate: string;
  appointmentTime: string;
  notes?: string;
  propertyUrl: string;
  rescheduleUrl: string;
  cancelUrl: string;
}

export const AppointmentConfirmationEmail = ({
  customerName,
  agentName,
  agentPhone,
  agentEmail,
  propertyTitle,
  propertyAddress,
  appointmentDate,
  appointmentTime,
  notes,
  propertyUrl,
  rescheduleUrl,
  cancelUrl,
}: AppointmentConfirmationEmailProps) => (
  <Html>
    <Head />
    <Preview>
      Property viewing confirmed for {appointmentDate} at {appointmentTime}
    </Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>✅ Appointment Confirmed</Heading>

        <Text style={text}>Hi {customerName},</Text>

        <Text style={text}>
          Your property viewing has been confirmed! Here are the details:
        </Text>

        <Section style={appointmentCard}>
          <Heading style={h2}>{propertyTitle}</Heading>
          <Text style={appointmentDetails}>
            📍 <strong>Address:</strong> {propertyAddress}
            <br />📅 <strong>Date:</strong> {appointmentDate}
            <br />🕒 <strong>Time:</strong> {appointmentTime}
          </Text>

          <Button href={propertyUrl} style={button}>
            View Property Details
          </Button>
        </Section>

        <Hr style={hr} />

        <Section style={agentSection}>
          <Heading style={h3}>Your Agent</Heading>
          <Text style={agentDetails}>
            <strong>{agentName}</strong>
            <br />📞 {agentPhone}
            <br />
            ✉️ {agentEmail}
          </Text>
        </Section>

        {notes && (
          <>
            <Hr style={hr} />
            <Section style={notesSection}>
              <Heading style={h3}>Additional Notes</Heading>
              <Text style={notesText}>{notes}</Text>
            </Section>
          </>
        )}

        <Hr style={hr} />

        <Section style={actionsSection}>
          <Text style={text}>
            <strong>Need to make changes?</strong>
          </Text>

          <div style={buttonGroup}>
            <Button href={rescheduleUrl} style={secondaryButton}>
              Reschedule
            </Button>
            <Button href={cancelUrl} style={cancelButton}>
              Cancel
            </Button>
          </div>
        </Section>

        <Hr style={hr} />

        <Section style={reminderSection}>
          <Text style={reminderText}>
            <strong>Viewing Tips:</strong>
            <br />• Arrive a few minutes early
            <br />• Bring a valid ID
            <br />• Feel free to ask questions about the property
            <br />• Take photos if you'd like (with permission)
          </Text>
        </Section>

        <Text style={footerText}>
          We're excited to help you find your perfect home!
          <br />
          The Homeo Team
        </Text>
      </Container>
    </Body>
  </Html>
);

AppointmentConfirmationEmail.PreviewProps = {
  customerName: 'John Smith',
  agentName: 'Sarah Johnson',
  agentPhone: '+****************',
  agentEmail: '<EMAIL>',
  propertyTitle: 'Beautiful 3BR Family Home in Downtown',
  propertyAddress: '123 Main Street, Toronto, ON',
  appointmentDate: 'Saturday, March 15, 2024',
  appointmentTime: '2:00 PM - 2:30 PM',
  notes:
    'Please park in the driveway. The property has a dog, so please let me know if you have any allergies.',
  propertyUrl: 'https://homeo.com/property/123',
  rescheduleUrl: 'https://homeo.com/appointment/reschedule/abc123',
  cancelUrl: 'https://homeo.com/appointment/cancel/abc123',
} as AppointmentConfirmationEmailProps;

export default AppointmentConfirmationEmail;

const main = {
  backgroundColor: '#ffffff',
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: '0 auto',
  padding: '20px 0 48px',
  maxWidth: '560px',
};

const h1 = {
  color: '#333',
  fontSize: '24px',
  fontWeight: '600',
  lineHeight: '40px',
  margin: '0 0 20px',
};

const h2 = {
  color: '#333',
  fontSize: '20px',
  fontWeight: '600',
  lineHeight: '28px',
  margin: '0 0 12px',
};

const h3 = {
  color: '#333',
  fontSize: '18px',
  fontWeight: '600',
  lineHeight: '24px',
  margin: '0 0 12px',
};

const text = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '26px',
  margin: '0 0 20px',
};

const appointmentCard = {
  backgroundColor: '#e8f5e8',
  borderRadius: '8px',
  padding: '20px',
  margin: '20px 0',
  border: '1px solid #c3e6c3',
};

const appointmentDetails = {
  color: '#333',
  fontSize: '16px',
  lineHeight: '24px',
  margin: '0 0 16px',
};

const agentSection = {
  margin: '20px 0',
};

const agentDetails = {
  color: '#333',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
};

const notesSection = {
  margin: '20px 0',
};

const notesText = {
  backgroundColor: '#f8f9fa',
  borderLeft: '4px solid #007bff',
  padding: '12px 16px',
  margin: '0',
  fontStyle: 'italic',
  color: '#555',
  fontSize: '14px',
  lineHeight: '20px',
};

const actionsSection = {
  margin: '20px 0',
  textAlign: 'center' as const,
};

const buttonGroup = {
  display: 'flex',
  gap: '12px',
  justifyContent: 'center',
  flexWrap: 'wrap' as const,
};

const button = {
  backgroundColor: '#007bff',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '16px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '12px 20px',
  fontWeight: '600',
  margin: '0 6px 12px',
};

const secondaryButton = {
  backgroundColor: '#6c757d',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '14px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 16px',
  fontWeight: '600',
  margin: '0 6px',
};

const cancelButton = {
  backgroundColor: '#dc3545',
  borderRadius: '6px',
  color: '#fff',
  fontSize: '14px',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  padding: '10px 16px',
  fontWeight: '600',
  margin: '0 6px',
};

const reminderSection = {
  backgroundColor: '#fff3cd',
  borderRadius: '8px',
  padding: '16px',
  margin: '20px 0',
  border: '1px solid #ffeaa7',
};

const reminderText = {
  color: '#856404',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '0',
};

const footerText = {
  color: '#666',
  fontSize: '14px',
  lineHeight: '20px',
  margin: '20px 0 0',
  textAlign: 'center' as const,
};

const hr = {
  borderColor: '#eee',
  margin: '20px 0',
};
