{"name": "@repo/ui", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "sideEffects": ["./src/styles/*.css"], "exports": {"./components/*": "./src/components/*.tsx", "./hooks/*": "./src/hooks/*.tsx", "./lib/*": "./src/lib/*.ts", "./styles/globals.css": "./src/styles/globals.css", "./postcss.config": "./postcss.config.mjs"}, "scripts": {"generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "typescript": "5.9.2"}, "dependencies": {"react": "^19.1.0", "react-dom": "^19.1.0", "clsx": "^2.1.1", "tailwind-merge": "^2.5.4", "class-variance-authority": "^0.7.1", "@radix-ui/react-slot": "^1.1.1"}}