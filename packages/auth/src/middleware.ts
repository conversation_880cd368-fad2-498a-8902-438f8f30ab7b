import type { Context, Next } from 'hono';
import { auth } from './config';

export async function authMiddleware(c: Context, next: Next) {
  try {
    const session = await auth.api.getSession({
      headers: c.req.raw.headers,
    });

    if (session) {
      c.set('user', session.user);
      c.set('session', session.session);
    }
  } catch (_error) {}

  await next();
}

export async function requireAuth(c: Context, next: Next) {
  const user = c.get('user');

  if (!user) {
    return c.json({ error: 'Authentication required' }, 401);
  }

  await next();
}
