{"name": "@repo/auth", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./config": "./src/config.ts", "./middleware": "./src/middleware.ts", "./types": "./src/types.ts"}, "scripts": {"check-types": "tsc --noEmit"}, "devDependencies": {"@better-auth/cli": "^1.3.7", "@repo/typescript-config": "*", "@types/node": "^22.15.3", "dotenv": "^17.2.1", "typescript": "5.9.2", "zod": "^4.1.3"}, "dependencies": {"@repo/db": "*", "better-auth": "^1.3.7"}}