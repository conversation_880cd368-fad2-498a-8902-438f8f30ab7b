{"name": "@repo/analytics", "version": "0.0.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": "./src/index.ts", "./client": "./src/client.ts", "./server": "./src/server.ts", "./events": "./src/events.ts"}, "scripts": {"check-types": "tsc --noEmit"}, "devDependencies": {"@repo/typescript-config": "*", "@types/node": "^22.15.3", "typescript": "5.9.2"}, "dependencies": {"posthog-js": "^1.188.1", "posthog-node": "^4.3.1"}}