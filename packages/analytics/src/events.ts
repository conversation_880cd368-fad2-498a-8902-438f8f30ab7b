// Real Estate Events
export const EVENTS = {
  // User Authentication
  USER_SIGNED_UP: 'User Signed Up',
  USER_SIGNED_IN: 'User Signed In',
  USER_SIGNED_OUT: 'User Signed Out',

  // Listing Events
  LISTING_VIEWED: 'Listing Viewed',
  LISTING_CREATED: 'Listing Created',
  LISTING_UPDATED: 'Listing Updated',
  LISTING_DELETED: 'Listing Deleted',
  LISTING_FAVORITED: 'Listing Favorited',
  LISTING_UNFAVORITED: 'Listing Unfavorited',

  // Search Events
  SEARCH_PERFORMED: 'Search Performed',
  SEARCH_FILTERED: 'Search Filtered',
  SEARCH_SAVED: 'Search Saved',

  // Contact Events
  CONTACT_FORM_SUBMITTED: 'Contact Form Submitted',
  PHONE_CLICKED: 'Phone Number Clicked',
  EMAIL_CLICKED: 'Email Address Clicked',

  // Page Views
  PAGE_VIEWED: 'Page Viewed',
  HOM<PERSON>_PAGE_VIEWED: 'Home Page Viewed',
  LISTING_PAGE_VIEWED: 'Listing Page Viewed',
  SEARCH_PAGE_VIEWED: 'Search Page Viewed',
} as const;

export type EventName = (typeof EVENTS)[keyof typeof EVENTS];

// Event Properties
export interface ListingViewedProperties {
  listing_id: string;
  listing_title: string;
  listing_price: number;
  listing_type: string;
  listing_status: string;
  source?: string;
}

export interface SearchPerformedProperties {
  query?: string;
  filters: {
    property_type?: string;
    min_price?: number;
    max_price?: number;
    bedrooms?: number;
    bathrooms?: number;
  };
  results_count: number;
}

export interface UserSignedUpProperties {
  method: 'email' | 'google' | 'github';
  user_id: string;
  email: string;
}
