import posthog from 'posthog-js';

export class AnalyticsClient {
  private static instance: AnalyticsClient;
  private initialized = false;

  private constructor() {}

  static getInstance(): AnalyticsClient {
    if (!AnalyticsClient.instance) {
      AnalyticsClient.instance = new AnalyticsClient();
    }
    return AnalyticsClient.instance;
  }

  init(apiKey?: string, options?: any) {
    if (this.initialized) {
      return;
    }

    const key = apiKey || process.env.NEXT_PUBLIC_POSTHOG_KEY;
    if (!key) {
      return;
    }

    posthog.init(key, {
      api_host:
        process.env.NEXT_PUBLIC_POSTHOG_HOST || 'https://us.i.posthog.com',
      person_profiles: 'identified_only',
      capture_pageview: false, // We'll handle this manually
      ...options,
    });

    this.initialized = true;
  }

  identify(userId: string, properties?: Record<string, any>) {
    if (!this.initialized) {
      return;
    }
    posthog.identify(userId, properties);
  }

  track(event: string, properties?: Record<string, any>) {
    if (!this.initialized) {
      return;
    }
    posthog.capture(event, properties);
  }

  page(name?: string, properties?: Record<string, any>) {
    if (!this.initialized) {
      return;
    }
    posthog.capture('$pageview', { $current_url: name, ...properties });
  }

  alias(alias: string) {
    if (!this.initialized) {
      return;
    }
    posthog.alias(alias);
  }

  reset() {
    if (!this.initialized) {
      return;
    }
    posthog.reset();
  }

  group(groupType: string, groupKey: string, properties?: Record<string, any>) {
    if (!this.initialized) {
      return;
    }
    posthog.group(groupType, groupKey, properties);
  }
}

export const analytics = AnalyticsClient.getInstance();
