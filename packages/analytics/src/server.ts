import { PostHog } from 'posthog-node';

export class AnalyticsServer {
  private readonly client: PostHog;

  constructor(apiKey?: string) {
    const key = apiKey || process.env.POSTHOG_API_KEY;
    if (!key) {
      throw new Error('PostHog API key is required for server-side analytics');
    }

    this.client = new PostHog(key, {
      host: process.env.POSTHOG_HOST || 'https://us.i.posthog.com',
    });
  }

  identify(options: { distinctId: string; properties?: Record<string, any> }) {
    this.client.identify({
      distinctId: options.distinctId,
      properties: options.properties,
    });
  }

  track(options: {
    distinctId: string;
    event: string;
    properties?: Record<string, any>;
  }) {
    this.client.capture({
      distinctId: options.distinctId,
      event: options.event,
      properties: options.properties,
    });
  }

  alias(options: { distinctId: string; alias: string }) {
    this.client.alias({
      distinctId: options.distinctId,
      alias: options.alias,
    });
  }

  group(options: {
    distinctId: string;
    groupType: string;
    groupKey: string;
    properties?: Record<string, any>;
  }) {
    this.client.groupIdentify({
      groupType: options.groupType,
      groupKey: options.groupKey,
      properties: options.properties,
    });
  }

  async shutdown() {
    await this.client.shutdown();
  }
}

// Default instance
export const analyticsServer = new AnalyticsServer();
