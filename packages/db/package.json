{"name": "@repo/db", "version": "0.0.0", "private": true, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.js", "default": "./dist/index.js"}}, "type": "module", "scripts": {"db:generate": "bunx drizzle-kit generate", "db:migrate": "bunx drizzle-kit migrate", "db:push": "bunx drizzle-kit push", "db:studio": "bunx drizzle-kit studio", "db:drop": "bunx drizzle-kit drop", "db:seed": "bun run src/seed.ts", "db:seed:dev": "bun run src/seed.ts --preset=dev", "db:seed:test": "bun run src/seed.ts --preset=test", "db:seed:demo": "bun run src/seed.ts --preset=demo", "db:seed:ci": "bun run src/seed.ts --preset=ci", "check-types": "tsc --noEmit", "build": "tsup"}, "dependencies": {"dotenv": "^17.2.1", "pg": "^8.16.3", "drizzle-orm": "0.44.5"}, "devDependencies": {"@repo/typescript-config": "*", "@types/node": "^22.15.3", "@types/pg": "^8.15.5", "drizzle-kit": "^0.31.4", "typescript": "5.9.2", "tsup": "^8.5.0"}, "types": "dist/index.d.ts"}