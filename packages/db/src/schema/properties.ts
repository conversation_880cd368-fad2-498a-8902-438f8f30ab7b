import { relations } from 'drizzle-orm';
import {
  integer,
  pgEnum,
  pgTable,
  real,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { features } from './features';
import { listings } from './listings';
import { locations } from './locations';
import { media } from './media';

export const propertyTypeEnum = pgEnum('property_types', [
  'HOUSE',
  'APARTMENT',
  'CONDO',
  'TOWNHOUSE',
]);

export const propertyConditionEnum = pgEnum('property_conditions', [
  'NEW',
  'GOOD',
  'FAIR',
  'NEEDS_WORK',
]);

export const propertyStyleEnum = pgEnum('property_styles', [
  'MODERN',
  'TRADITIONAL',
  'CONTEMPORARY',
]);

export const properties = pgTable('properties', {
  id: text('id').primaryKey(),
  bedrooms: integer('bedrooms').notNull(),
  bathrooms: integer('bathrooms').notNull(),
  propertyType: propertyTypeEnum('property_type').notNull(),
  squareMeters: real('square_meters').notNull(),
  lotSize: real('lot_size').notNull(),
  yearBuilt: integer('year_built').notNull(),
  floorCount: integer('floor_count').notNull(),
  propertyCondition: propertyConditionEnum('property_condition').notNull(),
  style: propertyStyleEnum('style'),
  locationId: text('location_id').notNull().unique(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const propertiesRelations = relations(properties, ({ one, many }) => ({
  location: one(locations, {
    fields: [properties.locationId],
    references: [locations.id],
  }),
  listings: many(listings),
  media: many(media),
  features: many(features),
}));
