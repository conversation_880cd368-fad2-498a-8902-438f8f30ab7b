import {
  boolean,
  integer,
  pgTable,
  text,
  timestamp,
} from 'drizzle-orm/pg-core';

export const agencies = pgTable('agencies', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  email: text('email').notNull().unique(),
  phone: text('phone'),
  website: text('website'),
  address: text('address'),
  city: text('city'),
  state: text('state'),
  country: text('country').notNull().default('USA'),
  postalCode: text('postal_code'),
  licenseNumber: text('license_number').unique(),
  establishedYear: integer('established_year'),
  isActive: boolean('is_active').notNull().default(true),
  isVerified: boolean('is_verified').notNull().default(false),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});
