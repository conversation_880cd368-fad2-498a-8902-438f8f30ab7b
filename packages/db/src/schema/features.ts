import { relations } from 'drizzle-orm';
import { pgEnum, pgTable, text } from 'drizzle-orm/pg-core';
import { properties } from './properties';

export const featureCategoryEnum = pgEnum('feature_categories', [
  'INTERIOR',
  'EXTERIOR',
  'COMMUNITY',
  'UTILITIES',
]);

export const features = pgTable('features', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  category: featureCategoryEnum('category').notNull(),
});

export const featuresRelations = relations(features, ({ many }) => ({
  properties: many(properties),
}));
