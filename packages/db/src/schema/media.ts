import { relations } from 'drizzle-orm';
import { integer, pgEnum, pgTable, text, uuid } from 'drizzle-orm/pg-core';
import { properties } from './properties';

export const mediaTypeEnum = pgEnum('media_types', [
  'IMAGE',
  'VIDEO',
  'VIRTUAL_TOUR',
  'FLOOR_PLAN',
]);

export const media = pgTable('media', {
  id: text('id').primaryKey(),
  url: text('url').notNull(),
  type: mediaTypeEnum('type').notNull(),
  caption: text('caption'),
  order: integer('order').notNull(),
  propertyId: text('property_id').notNull(),
});

export const mediaRelations = relations(media, ({ one }) => ({
  property: one(properties, {
    fields: [media.propertyId],
    references: [properties.id],
  }),
}));
