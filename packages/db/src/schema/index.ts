import * as accounts from './accounts';
import * as agencies from './agencies';
import * as features from './features';
import * as listings from './listings';
import * as locations from './locations';
import * as media from './media';
import * as properties from './properties';
import * as sessions from './sessions';
import * as users from './users';
import * as verifications from './verifications';

export * from './accounts';
export * from './agencies';
export * from './features';
export * from './listings';
export * from './locations';
export * from './media';
export * from './properties';
export * from './sessions';
export * from './users';
export * from './verifications';

export type Schema = typeof schema;

const schema = {
  ...accounts,
  ...agencies,
  ...features,
  ...listings,
  ...locations,
  ...media,
  ...properties,
  ...sessions,
  ...users,
  ...verifications,
};

export default schema;