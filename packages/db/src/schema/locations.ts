import { relations } from 'drizzle-orm';
import {
  index,
  pgTable,
  real,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { properties } from './properties';

export const locations = pgTable(
  'locations',
  {
    id: text('id').primaryKey(),
    address: text('address').notNull(),
    city: text('city').notNull(),
    state: text('state'),
    country: text('country').notNull(),
    postalCode: text('postal_code'),
    latitude: real('latitude'),
    longitude: real('longitude'),
    neighborhood: text('neighborhood'),
    district: text('district'),
    createdAt: timestamp('created_at').notNull().defaultNow(),
    updatedAt: timestamp('updated_at').notNull().defaultNow(),
  },
  (table) => ({
    cityCountryIdx: index('locations_city_country_idx').on(
      table.city,
      table.country
    ),
    latLonIdx: index('locations_lat_lon_idx').on(
      table.latitude,
      table.longitude
    ),
  })
);

export const locationsRelations = relations(locations, ({ one }) => ({
  property: one(properties, {
    fields: [locations.id],
    references: [properties.locationId],
  }),
}));
