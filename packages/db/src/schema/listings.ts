import { relations } from 'drizzle-orm';
import {
  boolean,
  pgEnum,
  pgTable,
  real,
  text,
  timestamp,
  uuid,
} from 'drizzle-orm/pg-core';
import { properties } from './properties';

export const listingStatusEnum = pgEnum('listing_statuses', [
  'ACTIVE',
  'DRAFT',
  'PENDING',
  'SOLD',
]);

export const listingTypeEnum = pgEnum('listing_types', ['SALE', 'RENT']);

export const listings = pgTable('listings', {
  id: text('id').primaryKey(),
  slug: text('slug').notNull().unique(),
  title: text('title').notNull(),
  description: text('description').notNull(),
  price: real('price').notNull(),
  currency: text('currency').notNull(),
  address: text('address').notNull(),
  status: listingStatusEnum('status').notNull().default('DRAFT'),
  listingType: listingTypeEnum('listing_type').notNull(),
  availableFrom: timestamp('available_from'),
  expiresAt: timestamp('expires_at'),
  isFeatured: boolean('is_featured').notNull().default(false),
  propertyId: text('property_id').notNull(),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

export const listingsRelations = relations(listings, ({ one }) => ({
  property: one(properties, {
    fields: [listings.propertyId],
    references: [properties.id],
  }),
}));
