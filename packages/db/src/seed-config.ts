export interface SeedConfig {
  /** Number of records to generate */
  count: number;
  /** Clear existing data before seeding */
  clearExisting: boolean;
  /** Show progress logs during seeding */
  showProgress: boolean;
  /** Progress log interval (every N records) */
  progressInterval: number;
  /** Data generation settings */
  data: {
    /** Probability settings (0-1) */
    probabilities: {
      /** Probability of having a district */
      district: number;
      /** Probability of having a property style */
      style: number;
      /** Probability of having availableFrom date */
      availableFrom: number;
      /** Probability of having expiresAt date */
      expiresAt: number;
      /** Probability of being featured (lower = more likely) */
      featured: number;
    };
    /** Property constraints */
    property: {
      bedrooms: { min: number; max: number };
      bathrooms: { min: number; max: number };
      squareMeters: { min: number; range: number };
      lotSize: { min: number; range: number };
      yearBuilt: { min: number; range: number };
      floorCount: { min: number; max: number };
    };
    /** Price ranges by property type and listing type */
    pricing: {
      rent: {
        apartment: { base: number; range: number };
        condo: { base: number; range: number };
        townhouse: { base: number; range: number };
        house: { base: number; range: number };
        default: { base: number; range: number };
      };
      sale: {
        apartment: { base: number; range: number };
        condo: { base: number; range: number };
        townhouse: { base: number; range: number };
        house: { base: number; range: number };
        default: { base: number; range: number };
      };
    };
    /** Location settings */
    location: {
      streetNumber: { max: number };
      postalCode: { min: number; range: number };
      coordinates: {
        latitude: { min: number; range: number };
        longitude: { min: number; range: number };
      };
    };
    /** Expiration settings */
    expiration: {
      daysFromNow: number;
    };
  };
}

export const defaultSeedConfig: SeedConfig = {
  count: 100,
  clearExisting: true,
  showProgress: true,
  progressInterval: 10,
  data: {
    probabilities: {
      district: 0.5,
      style: 0.3,
      availableFrom: 0.5,
      expiresAt: 0.7,
      featured: 0.8,
    },
    property: {
      bedrooms: { min: 1, max: 5 },
      bathrooms: { min: 1, max: 4 },
      squareMeters: { min: 50, range: 400 },
      lotSize: { min: 100, range: 1000 },
      yearBuilt: { min: 1950, range: 74 },
      floorCount: { min: 1, max: 3 },
    },
    pricing: {
      rent: {
        apartment: { base: 1500, range: 2000 },
        condo: { base: 2000, range: 2500 },
        townhouse: { base: 2500, range: 2000 },
        house: { base: 3000, range: 4000 },
        default: { base: 2000, range: 2000 },
      },
      sale: {
        apartment: { base: 200_000, range: 400_000 },
        condo: { base: 300_000, range: 500_000 },
        townhouse: { base: 400_000, range: 600_000 },
        house: { base: 500_000, range: 1_000_000 },
        default: { base: 300_000, range: 400_000 },
      },
    },
    location: {
      streetNumber: { max: 9999 },
      postalCode: { min: 10_000, range: 89_999 },
      coordinates: {
        latitude: { min: 25, range: 25 },
        longitude: { min: -125, range: 60 },
      },
    },
    expiration: {
      daysFromNow: 90,
    },
  },
};

/**
 * Load seed configuration from environment variables or use defaults
 */
export function loadSeedConfig(): SeedConfig {
  const config = { ...defaultSeedConfig };

  // Override with environment variables if provided
  if (process.env.SEED_COUNT) {
    config.count = Number.parseInt(process.env.SEED_COUNT, 10);
  }

  if (process.env.SEED_CLEAR_EXISTING) {
    config.clearExisting = process.env.SEED_CLEAR_EXISTING === 'true';
  }

  if (process.env.SEED_SHOW_PROGRESS) {
    config.showProgress = process.env.SEED_SHOW_PROGRESS === 'true';
  }

  if (process.env.SEED_PROGRESS_INTERVAL) {
    config.progressInterval = Number.parseInt(
      process.env.SEED_PROGRESS_INTERVAL,
      10
    );
  }

  return config;
}

/**
 * Seed configuration presets for different scenarios
 */
export const seedPresets = {
  /** Small dataset for development */
  dev: {
    ...defaultSeedConfig,
    count: 10,
    showProgress: true,
  } as SeedConfig,

  /** Medium dataset for testing */
  test: {
    ...defaultSeedConfig,
    count: 50,
    showProgress: false,
  } as SeedConfig,

  /** Large dataset for production demo */
  demo: {
    ...defaultSeedConfig,
    count: 500,
    showProgress: true,
    progressInterval: 25,
  } as SeedConfig,

  /** Minimal dataset for CI/CD */
  ci: {
    ...defaultSeedConfig,
    count: 5,
    showProgress: false,
    clearExisting: true,
  } as SeedConfig,
} as const;

export type SeedPreset = keyof typeof seedPresets;
