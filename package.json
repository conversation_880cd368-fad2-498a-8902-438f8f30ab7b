{"name": "homeo", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "format": "npx ultracite@latest format", "lint": "npx ultracite@latest lint", "check-types": "turbo run check-types"}, "devDependencies": {"@biomejs/biome": "2.2.0", "drizzle-kit": "^0.31.4", "husky": "^9.1.7", "turbo": "^2.5.6", "typescript": "5.9.2", "ultracite": "5.2.4"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.20", "workspaces": ["apps/*", "packages/*"], "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-orm": "0.44.5", "lucide-react": "^0.540.0", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.7"}}