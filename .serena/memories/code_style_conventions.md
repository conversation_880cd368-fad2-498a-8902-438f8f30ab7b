# Code Style and Conventions

## Linting and Formatting
- **Tool**: Ultracite (built on Biome)
- **Configuration**: `biome.jsonc`
- **Strict TypeScript**: Enabled across all packages
- **Pre-commit hooks**: Configured with Husky

## Key Conventions
- Use TypeScript with strict settings
- Follow Biome/Ultracite rules (comprehensive accessibility, React best practices, TypeScript guidelines)
- Use `const` for variables that don't change
- Prefer arrow functions over function declarations
- Use template literals over string concatenation
- Follow consistent naming conventions
- Export types using `export type`
- Use `import type` for type-only imports

## File Structure
- Barrel exports from packages (e.g., `@repo/db`)
- Workspace dependencies properly configured
- Shared configs in `@repo/typescript-config`

## Database Conventions
- Prisma ORM with PostgreSQL
- UUID(7) for primary keys
- Snake_case for database table names (via @@map)
- Proper indexing for performance