# Task Completion Checklist

When completing a coding task, always run:

1. **Format code**: `npx ultracite@latest format`
2. **Lint code**: `npx ultracite@latest lint` 
3. **Type check**: `turbo run check-types`
4. **For database changes**: 
   - Generate Prisma client: `bunx prisma generate`
   - Run migrations: `bunx prisma migrate dev`
5. **Test applications**:
   - API: `turbo dev --filter=api`
   - Web: `turbo dev --filter=web`

## Environment Requirements
- `DATABASE_URL` environment variable must be set
- Bun runtime required
- PostgreSQL database accessible

## Pre-commit
- <PERSON><PERSON> hooks automatically run formatting/linting
- Ensure all checks pass before committing