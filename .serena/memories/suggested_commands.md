# Key Development Commands

## Global Commands (from root)
- `bun install` - Install dependencies
- `bun dev` or `turbo dev` - Run all apps in development
- `bun run build` or `turbo build` - Build all applications
- `bun run lint` or `npx ultracite@latest lint` - Lint all code
- `bun run format` or `npx ultracite@latest format` - Format all code
- `bun run check-types` or `turbo run check-types` - Type checking

## Filtered Commands
- `turbo dev --filter=web` - Run Next.js app only
- `turbo dev --filter=api` - Run Hono API only
- `turbo build --filter=@repo/db` - Build specific package

## Database Commands (from packages/db)
- `bunx prisma generate` - Generate Prisma client
- `bunx prisma migrate dev` - Run migrations in development
- `bunx prisma db push` - Push schema changes to database
- `bunx prisma studio` - Open Prisma Studio
- `bunx prisma migrate reset` - Reset database
- `bun run db:setup` - Full database setup

## System Commands (Darwin/macOS)
- `ls`, `cd`, `grep`, `find` - Standard Unix commands
- `git` - Version control