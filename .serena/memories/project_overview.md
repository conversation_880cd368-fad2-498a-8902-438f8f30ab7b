# Homeo - Real Estate Platform

## Purpose
Homeo is a real estate platform built with TypeScript monorepo structure using Turborepo. It manages property listings and real estate data.

## Tech Stack
- **Package Manager**: Bun
- **Build System**: Turborepo
- **Database**: PostgreSQL with Prisma ORM
- **API Framework**: Hono (apps/api)
- **Frontend**: Next.js 14+ with App Router (apps/web)
- **Styling**: Tailwind CSS
- **Language**: TypeScript
- **Linting/Formatting**: Ultracite (Biome)

## Project Structure
```
homeo/
├── apps/
│   ├── api/          # Hono-based REST API
│   └── web/          # Next.js frontend
├── packages/
│   ├── auth/         # Auth package
│   ├── db/           # Database layer with Prisma ORM
│   ├── ui/           # Shared React components
│   ├── mail/         # Mail utilities
│   ├── analytics/    # Analytics package
│   └── typescript-config/  # Shared TypeScript configs
```

## Database Schema
Uses Prisma with PostgreSQL. Key models:
- Listing (with properties like title, description, price, status, type)
- Property (bedrooms, bathrooms, type, condition, style)
- Location (address, coordinates, city, country)
- Media, Features, etc.